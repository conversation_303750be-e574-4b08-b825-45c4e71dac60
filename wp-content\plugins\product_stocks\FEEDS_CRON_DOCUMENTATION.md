# Документация за автоматично регенериране на XML фийдове

## Общ преглед

Плъгинът product_stocks включва функционалност за автоматично регенериране на XML фийдове на всеки 8 часа (3 пъти дневно). Тази функционалност използва WordPress cron система за планирано изпълнение.

## Архитектура

### Основни компоненти:

1. **WordPress Cron Job** (`av_ps_feeds_auto_refresh`)
   - Интервал: 8 часа (28800 секунди)
   - Първо изпълнение: 1 час след активиране на плъгина
   - Персонализиран интервал: `av_ps_feeds_8hours`

2. **Action Hook**
   - Hook: `av_ps_feeds_auto_refresh`
   - Callback: `AV_PS_Rest_Feed_Controller::auto_refresh_feeds()`

3. **Логване**
   - Файл: `wp-content/plugins/product_stocks/logs/feeds_auto_refresh.txt`
   - Пълно логване на всички операции
   - Timestamp за начало и край на процеса

## Файлове и класове

### Основни файлове:
- `product_stocks.php` - Инициализация на cron задачата
- `classes/rest_feed_controller.php` - Изпълнение на автоматичното обновяване
- `classes/model/feeds.php` - Генериране на XML фийдовете
- `test_feeds_cron.php` - Тестов файл за проверка и управление

### Ключови методи:

#### В `AV_ProductStocks` класа:
- `init_feeds_auto_refresh_cron()` - Инициализира cron задачата
- `add_feeds_cron_interval()` - Добавя персонализиран 8-часов интервал

#### В `AV_PS_Rest_Feed_Controller` класа:
- `auto_refresh_feeds()` - Основен метод за автоматично обновяване
- `getActiveVendors()` - Извлича активните търговци

#### В `AV_WCProduct_Model_Feeds` класа:
- `generateAndCacheFeed()` - Генерира и кешира XML фийд за търговец

## Логване

### Типове логове:
1. **Начало на процеса** - Timestamp и общ статус
2. **Активни търговци** - Брой и имена на търговците
3. **Индивидуални резултати** - За всеки търговец поотделно
4. **Финален резултат** - Общ статус, време за изпълнение, статистики

### Пример лог запис:
```
=== ЗАПОЧВА АВТОМАТИЧНО ОБНОВЯВАНЕ НА XML ФИЙДОВЕ ===
Време на започване: 2024-01-15 14:30:00
Намерени 2 активни търговци: ozone, emag
✅ Успешно обновен фийд за търговец: ozone | Продукти: 1250 | Размер: 2.5 MB | Време: 15.3с
✅ Успешно обновен фийд за търговец: emag | Продукти: 980 | Размер: 1.8 MB | Време: 12.1с
=== ЗАВЪРШВА АВТОМАТИЧНО ОБНОВЯВАНЕ - УСПЕШНО ===
Време на завършване: 2024-01-15 14:30:45
Общо време за изпълнение: 27.4 секунди
Общо търговци: 2 | Успешни: 2 | Грешки: 0
```

## Управление и тестване

### Тестов файл (`test_feeds_cron.php`)

Достъпен на: `http://yourdomain.com/wp-content/plugins/product_stocks/test_feeds_cron.php`

#### Функции:
1. **Състояние** (`?action=status`)
   - Проверка на cron статуса
   - Информация за активните търговци
   - Следващо планирано изпълнение

2. **Тестово изпълнение** (`?action=test`)
   - Ръчно стартиране на автоматичното обновяване
   - Измерване на времето за изпълнение
   - Директен достъп до логовете

3. **Инициализиране** (`?action=init`)
   - Създаване на нова cron задача
   - Преинициализиране на съществуваща задача

4. **Логове** (`?action=logs`)
   - Преглед на последните 100 реда от лога
   - Изтегляне на пълния лог файл
   - Изчистване на лог файла

### WordPress CLI команди

Използвайте съществуващите WP-CLI файлове:
- `temp_wp_cli_feeds_cron.php` - Управление чрез WP-CLI
- `temp_init_feeds_cron.php` - Инициализиране чрез браузър

## Конфигурация

### Настройки в кода:
```php
// Интервал между обновяванията (в секунди)
$interval = 28800; // 8 часа

// Забавяне преди първото изпълнение
$start_time = time() + HOUR_IN_SECONDS; // 1 час

// Име на cron hook-а
$hook_name = 'av_ps_feeds_auto_refresh';

// Име на персонализирания интервал
$interval_name = 'av_ps_feeds_8hours';
```

### Активиране/деактивиране:
- **Активиране**: Автоматично при активиране на плъгина
- **Деактивиране**: Автоматично при деактивиране на плъгина
- **Ръчно управление**: Чрез тестовия файл или WP-CLI

## Мониториране

### Проверка на състоянието:
1. Използвайте тестовия файл за бърза проверка
2. Проверете лог файла за детайли
3. Използвайте WordPress cron плъгини за мониториране

### Индикатори за проблеми:
- Липса на записи в лога
- Грешки в лог файла
- Неактивен cron статус в тестовия файл
- Остарели XML фийдове

## Отстраняване на проблеми

### Чести проблеми:

1. **Cron не се изпълнява**
   - Проверете WordPress cron настройките
   - Уверете се, че `DISABLE_WP_CRON` не е зададено на `true`
   - Проверете server cron настройките

2. **Няма логове**
   - Проверете правата за писане в logs директорията
   - Уверете се, че `IsDeveloper()` функцията работи правилно

3. **Грешки при генериране**
   - Проверете дали има настроени категории за търговците
   - Проверете базата данни за корупция
   - Проверете memory limit настройките

### Стъпки за отстраняване:
1. Проверете състоянието чрез тестовия файл
2. Стартирайте тестово изпълнение
3. Прегледайте логовете за грешки
4. При нужда преинициализирайте cron задачата

## Производителност

### Оптимизации:
- Кеширане на атрибути и категории
- Batch SQL заявки
- Директни SQL заявки вместо WordPress функции
- Ограничаване на логването само при нужда

### Мониториране на производителността:
- Време за изпълнение в логовете
- Размер на генерираните файлове
- Брой обработени продукти
- Memory usage (може да се добави при нужда)

## Сигурност

### Мерки за сигурност:
- Проверка за администраторски права в тестовия файл
- Токен-базирана автентикация за публичните фийдове
- Ограничен достъп до лог файловете
- Валидация на входните параметри

### Препоръки:
- Редовно архивиране на лог файловете
- Мониториране за необичайна активност
- Ограничаване на достъпа до тестовия файл в production
