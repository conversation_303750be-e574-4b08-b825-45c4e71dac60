<div id="ps_feeds_page">
    <h2>Фийдове</h2>
    
    <form id="ps_feeds_form" action="" method="post">
        <table class="wp-list-table widefat fixed striped ps_update_products">
            <tbody>
                <tr>
                    <td class="dis-area" colspan="2">
                        <strong>Търговец - OZone</strong>
                        <input type="hidden" id="current-vendor" value="ozone">
                    </td>
                </tr>
                <tr>    
                    <th scope="row"><label>Линк към фийда:</label></th>
                    <td class="dis-area">
                        <div class="feed-url-container">
                            <input type="text" id="feed-url" readonly class="regular-text" style="width: 80%;">
                            <button type="button" id="copy-feed-url" class="button button-secondary" style="margin-left: 10px;">
                                <span class="dashicons dashicons-clipboard" style="vertical-align: middle;"></span> Копирай
                            </button>
                        </div>
                        <p class="description">Това е публичният линк към XML фийда, който може да бъде предоставен на търговеца.</p>
                    </td>
                </tr>
                <tr>    
                    <th scope="row"><label for="category-search-input">Категории:</label></th>
                    <td class="dis-area">
                        <div style="position: relative; width: 100%;">
                            <input type="text" id="category-search-input" name="category_search_input" placeholder="Търсене на категория..." style="width: 100%; padding: 8px; box-sizing: border-box; border: 1px solid #ccc; border-radius: 4px;">
                            <div id="category-suggestions" style="display: none; position: absolute; top: 100%; left: 0; right: 0; border: 1px solid #ccc; border-top: none; background-color: white; z-index: 1000; max-height: 200px; overflow-y: auto; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-radius: 0 0 4px 4px;">
                                <!-- Предложенията ще се попълват тук от JavaScript -->
                            </div>
                        </div>
                        <!-- Скрито поле за съхранение на избраните категории като JSON -->
                        <input type="hidden" id="selected-categories" name="selected_categories" value="">
                        
                        <!-- Контейнер за показване на избраните категории като тагове -->
                        <div id="selected-categories-container" style="margin-top: 10px; display: flex; flex-wrap: wrap; gap: 8px;">
                            <!-- Тук ще се показват избраните категории като тагове -->
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <button type="button" id="save-categories" class="button button-primary">
                                <span class="dashicons dashicons-saved" style="vertical-align: middle;"></span> Запази избраните категории
                            </button>
                            <button type="button" id="refresh-feed" class="button button-secondary" style="margin-left: 10px;">
                                <span class="dashicons dashicons-update" style="vertical-align: middle;"></span> Обнови фийда
                            </button>

                            <!-- Лоудър за обновяване на фийда -->
                            <div id="feed-refresh-loader" style="display: none; margin-left: 15px;">
                                <div class="spinner" style="visibility: visible; float: none; margin: 0 5px 0 0;"></div>
                                <span>Обновява се фийдът...</span>
                            </div>
                        </div>
                        
                        <!-- Информация за последно генериране на кеша -->
                        <div id="feed-cache-info" style="margin-top: 15px; padding: 10px; background-color: #f8f8f8; border-radius: 4px; border: 1px solid #ddd;">
                            <p><strong>Статус на кеша:</strong> <span id="cache-status">Проверява се...</span></p>
                            <p><strong>Размер на файла:</strong> <span id="cache-size">-</span></p>
                            <p><strong>Последно генериран:</strong> <span id="last-generated">-</span></p>
                            <p><strong>Брой продукти:</strong> <span id="products-count">-</span></p>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>    
    </form>
</div>
