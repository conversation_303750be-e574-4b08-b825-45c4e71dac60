<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * REST API контролер за XML фийдове
 */
class AV_PS_Rest_Feed_Controller {
    
    private $namespace = 'product-feeds/v1';
    private $feeds_model;
    
    /**
     * Конструктор
     */
    public function __construct() {
        $this->feeds_model = new AV_WCProduct_Model_Feeds();
        add_action('rest_api_init', [$this, 'register_routes']);

        // Добавяме hook за автоматично обновяване на фийдовете
        add_action('av_ps_feeds_auto_refresh', [$this, 'auto_refresh_feeds']);
    }
    
    /**
     * Регистрира маршрутите за REST API
     */
    public function register_routes() {

        register_rest_route($this->namespace, '/(?P<vendor>[a-zA-Z0-9-_]+)', [
            'methods'  => WP_REST_Server::READABLE,
            'callback' => [$this, 'get_feed'],
            'permission_callback' => [$this, 'get_permission'],
            'args'     => [
                'vendor' => [
                    'required'          => true,
                    'validate_callback' => function($param) {
                        return is_string($param);
                    }
                ],
                'token' => [
                    'required'          => true,
                    'validate_callback' => function($param) {
                        return is_string($param);
                    }
                ],
            ],
        ]);
    }
    
    /**
     * Проверява правата за достъп до фийда
     * 
     * @param WP_REST_Request $request Заявката
     * @return bool Дали е разрешен достъпа
     */
    public function get_permission($request) {
        $vendor = $request->get_param('vendor');
        $token = $request->get_param('token');
        
        // Генерираме токена за сравнение
        $generated_token = $this->feeds_model->generateFeedToken($vendor);
        
        // Ако токените съвпадат, разрешаваме достъпа
        return $token === $generated_token;
    }
    
    /**
     * Обработва заявката за XML фийд
     * 
     * @param WP_REST_Request $request Заявката
     * @return WP_REST_Response Отговор с XML фийда
     */
    public function get_feed($request) {
        $vendor = $request->get_param('vendor');
        
        // Проверяваме статуса на кеша
        $cache_status = $this->feeds_model->getCacheStatus($vendor);
        
        // Ако кеша не е създаден или е остарял, генерираме нов
        if ($cache_status['needs_refresh']) {
            try {
                $this->feeds_model->generateAndCacheFeed($vendor);
                // Обновяваме статуса след генерирането
                $cache_status = $this->feeds_model->getCacheStatus($vendor);
            } catch (Exception $e) {
                return new WP_REST_Response([
                    'error' => true,
                    'message' => $e->getMessage()
                ], 500);
            }
        }
        
        // Ако файлът не съществува, връщаме грешка
        if (!$cache_status['exists']) {
            return new WP_REST_Response([
                'error' => true,
                'message' => 'Фийдът не е намерен'
            ], 404);
        }
        
        // Четем XML файла
        $xml_content = file_get_contents($cache_status['path']);
        
        // Правим директен изход с HTTP отговор без JSON обработка
        // Използваме exit за да предотвратим допълнителна обработка от WordPress
        
        // Добавяме заглавия за кеширане
        $max_age = 3600; // 1 час
        
        // Задаваме правилните HTTP заглавки
        header('Content-Type: application/xml; charset=UTF-8');
        header('Cache-Control: public, max-age=' . $max_age);
        
        // Директно изпращаме XML съдържанието без кавички
        echo $xml_content;
        exit;
    }

    /**
     * Автоматично обновяване на всички активни XML фийдове
     * Този метод се извиква от cron задачата на всеки 8 часа
     */
    public function auto_refresh_feeds() {
        // Записваме времето на започване
        $start_time = microtime(true);
        $start_timestamp = current_time('Y-m-d H:i:s');

        // Проверяваме дали заявката е от разработчик за логване
        $is_developer = IsDeveloper();

        // Логваме началото на процеса (винаги, не само за разработчик)
        AV_PS()->log_helper()->instantLog("=== ЗАПОЧВА АВТОМАТИЧНО ОБНОВЯВАНЕ НА XML ФИЙДОВЕ ===", 'feeds_auto_refresh.txt');
        AV_PS()->log_helper()->instantLog("Време на започване: {$start_timestamp}", 'feeds_auto_refresh.txt');

        // Получаваме всички активни търговци с фийдове
        $active_vendors = $this->getActiveVendors();

        if (empty($active_vendors)) {
            AV_PS()->log_helper()->instantLog('Няма активни търговци за обновяване на фийдове', 'feeds_auto_refresh.txt');
            AV_PS()->log_helper()->instantLog("=== ЗАВЪРШВА АВТОМАТИЧНО ОБНОВЯВАНЕ (БЕЗ ТЪРГОВЦИ) ===", 'feeds_auto_refresh.txt');
            return;
        }

        $success_count = 0;
        $error_count = 0;
        $total_vendors = count($active_vendors);

        AV_PS()->log_helper()->instantLog("Намерени {$total_vendors} активни търговци: " . implode(', ', $active_vendors), 'feeds_auto_refresh.txt');

        foreach ($active_vendors as $vendor) {
            $vendor_start_time = microtime(true);

            try {
                // Генерираме и кешираме фийда за търговеца
                $result = $this->feeds_model->generateAndCacheFeed($vendor);
                $success_count++;

                $vendor_execution_time = round(microtime(true) - $vendor_start_time, 2);
                $products_count = isset($result['products_count']) ? $result['products_count'] : 0;
                $file_size = isset($result['file_size']) ? $result['file_size'] : 'неизвестен';

                AV_PS()->log_helper()->instantLog("✅ Успешно обновен фийд за търговец: {$vendor} | Продукти: {$products_count} | Размер: {$file_size} | Време: {$vendor_execution_time}с", 'feeds_auto_refresh.txt');

            } catch (Exception $e) {
                $error_count++;
                $vendor_execution_time = round(microtime(true) - $vendor_start_time, 2);

                AV_PS()->log_helper()->instantLog("❌ Грешка при обновяване на фийд за търговец {$vendor} (време: {$vendor_execution_time}с): " . $e->getMessage(), 'feeds_auto_refresh.txt');
            }
        }

        // Изчисляваме общото време за изпълнение
        $total_execution_time = round(microtime(true) - $start_time, 2);
        $end_timestamp = current_time('Y-m-d H:i:s');

        // Логваме финалния резултат (винаги, не само за разработчик)
        $status = ($error_count === 0) ? 'УСПЕШНО' : 'С ГРЕШКИ';
        AV_PS()->log_helper()->instantLog("=== ЗАВЪРШВА АВТОМАТИЧНО ОБНОВЯВАНЕ - {$status} ===", 'feeds_auto_refresh.txt');
        AV_PS()->log_helper()->instantLog("Време на завършване: {$end_timestamp}", 'feeds_auto_refresh.txt');
        AV_PS()->log_helper()->instantLog("Общо време за изпълнение: {$total_execution_time} секунди", 'feeds_auto_refresh.txt');
        AV_PS()->log_helper()->instantLog("Общо търговци: {$total_vendors} | Успешни: {$success_count} | Грешки: {$error_count}", 'feeds_auto_refresh.txt');
        AV_PS()->log_helper()->instantLog("", 'feeds_auto_refresh.txt'); // Празен ред за разделяне
    }

    /**
     * Получава списък с всички активни търговци, които имат настроени категории
     *
     * @return array Масив с ключове на търговците
     */
    private function getActiveVendors() {
        global $wpdb;

        $sql = "SELECT DISTINCT vendor_key FROM {$wpdb->prefix}ps_vendor_feed_categories ORDER BY vendor_key";
        $results = $wpdb->get_col($sql);

        return $results ? $results : [];
    }
}
