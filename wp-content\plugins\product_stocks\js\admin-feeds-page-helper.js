var AV_PS_Admin_Feeds = (function($) {

    var base = null;
    var module = {
        // Съхраняваме избраните категории като обекти {id, name}
        selectedCategories: [],
        currentVendor: '',
        searchInput: null,
        suggestionsDiv: null,
        selectedCategoriesInput: null,
        selectedCategoriesContainer: null,
        debounceTimer: null,
        
        init: function() {
            if($('#ps_feeds_page').length) {
                if(!module.setBaseFunctionsObject()) {
                    return;
                }
                this.currentVendor = $('#current-vendor').val() || 'ozone';
                this.bindEvents();
                this.initializeComponents();
                this.loadFeedURL();
                this.loadCacheStatus();
                this.loadSavedCategories();

                console.log('AV_PS_Admin_Feeds init');
            }
        },
        
        setBaseFunctionsObject: function() {
            base = AV_PS_Admin;
            if(typeof base !== 'object') {
                console.log('AV_PS_Admin_Feeds Error: AV_PS_Admin is missing');
                return false;
            }
            return true;
        },

        initializeComponents: function() {
            this.initializeBaseFunctions();
            this.initializeCategoryAutocomplete();
            this.addCategoryAutocompleteStyles();
        },

        initializeBaseFunctions: function() {
            module.resetForm();
        },

        resetForm: function() {
            $('form#ps_feeds_form')[0].reset();
        },

        bindEvents: function() {
            $(window).on('pageshow', function(event) {
                module.resetForm();
            });
            $(document).off('click', '.page-title-action').on('click', '.page-title-action', module.handleActionClick);
            
            // События за копиране на URL адреса на фийда
            $('#copy-feed-url').on('click', module.copyFeedURL);
            
            // События за запазване на категориите
            $('#save-categories').on('click', module.saveSelectedCategories);
            
            // События за обновяване на фийда
            $('#refresh-feed').on('click', module.refreshFeedCache);
        },

        handleActionClick: function(e) {
            e.preventDefault();
            var $button = $(this);
            var action = $button.data('action');
            if (typeof module[action] === 'function') {
                module[action]($button);
            }
            else if(action) {
                base.ajaxCall({ element: $button, resultContainer: '.result-container', clearResultContainer: true }, action);
            }
        },
        
        // Копира URL адреса на фийда в клипборда
        copyFeedURL: function() {
            var feedUrl = $('#feed-url').val();
            if (feedUrl) {
                var tempInput = $('<input>');
                $('body').append(tempInput);
                tempInput.val(feedUrl).select();
                document.execCommand('copy');
                tempInput.remove();
                
                // Показваме нотификация
                alert('Линкът е копиран в клипборда!');
            }
        },
        
        // Зарежда URL на фийда чрез AJAX
        loadFeedURL: function() {
            var ajaxData = { vendor: module.currentVendor };
            base.ajaxCall({
                //loader_container: $('.feed-url-container'),
                //loader_html: '<em>Зареждане на URL...</em>'
            }, 
            'getFeedURL', 
            ajaxData, 
            function(success, response) {
                if (success && response.data && response.data.url) {
                    $('#feed-url').val(response.data.url);
                } else {
                    $('#feed-url').val('Грешка при зареждане на URL адреса');
                }
            });
        },
        
        // Зарежда статуса на кеша на фийда
        loadCacheStatus: function() {
            var ajaxData = { vendor: module.currentVendor };
            base.ajaxCall({
                // loader_container: $('#feed-cache-info'),
                // loader_html: '<em>Проверка на статуса...</em>'
            }, 
            'getCachedFeedStatus', 
            ajaxData, 
            function(success, response) {
                if (success && response.data) {
                    const data = response.data;
                    let statusText = data.exists ? (data.is_fresh ? 'Актуален' : 'Нуждае се от обновяване') : 'Не е генериран';
                    let statusClass = data.exists ? (data.is_fresh ? 'status-ok' : 'status-warning') : 'status-error';
                    
                    $('#cache-status').text(statusText).removeClass('status-ok status-warning status-error').addClass(statusClass);
                    $('#cache-size').text(data.size_formatted || 'Не е налично');
                    $('#last-generated').text(data.last_generated || 'Не е генериран');
                    $('#products-count').text(data.products_count || '-');
                } else {
                    $('#cache-status').text('Грешка при проверка').addClass('status-error');
                }
            });
        },
        
        // Зарежда запазените категории от базата данни
        loadSavedCategories: function() {
            var ajaxData = { vendor: module.currentVendor };
            base.ajaxCall({
                // loader_container: $('#selected-categories-container'),
                // loader_html: '<em>Зареждане на запазени категории...</em>'
            }, 
            'getSavedCategories', // използваме специален метод за зареждане на запазените категории
            ajaxData, 
            function(success, response) {
                if (success && response.data && response.data.categories) {
                    // Добавяме запазените категории към масива
                    module.selectedCategories = response.data.categories;
                    module.renderSelectedCategories();
                } else {
                    module.selectedCategories = [];
                    module.renderSelectedCategories();
                }
            });
        },
        
        // Запазва избраните категории
        saveSelectedCategories: function() {
            if (module.selectedCategories.length === 0) {
                alert('Моля, изберете поне една категория преди да запазите!');
                return;
            }
            
            var categoryIds = module.selectedCategories.map(function(cat) { return cat.id; });
            var ajaxData = { 
                vendor: module.currentVendor,
                categories: categoryIds
            };
            
            base.ajaxCall({
                // loader_container: $('#save-categories').parent(),
                // loader_html: '<em>Запазване на категориите...</em>',
                button: $('#save-categories'),
                disable_button: true
            }, 
            'saveCategories', 
            ajaxData, 
            function(success, response) {
                if (success && response.success) {
                    alert('Категориите са запазени успешно!');
                    // Обновяваме статуса на кеша след запазване
                    module.loadCacheStatus();
                } else {
                    alert('Възникна грешка при запазване на категориите: ' + 
                          (response.data && response.data.message ? response.data.message : 'неизвестна грешка'));
                }
            });
        },
        
        // Добавя категория към списъка с избрани
        addCategory: function(categoryId, categoryName) {
            // Проверяваме дали вече не съществува такава категория
            var exists = module.selectedCategories.some(function(cat) {
                return cat.id === categoryId;
            });
            
            if (!exists) {
                module.selectedCategories.push({
                    id: categoryId,
                    name: categoryName
                });
                
                module.renderSelectedCategories();
                return true;
            }
            
            return false;
        },
        
        // Премахва категория от списъка с избрани
        removeCategory: function(categoryId) {
            module.selectedCategories = module.selectedCategories.filter(function(cat) {
                return cat.id !== categoryId;
            });
            
            module.renderSelectedCategories();
        },
        
        // Визуализира избраните категории като тагове
        renderSelectedCategories: function() {
            var container = $('#selected-categories-container');
            container.empty();
            
            // Актуализираме скритото поле с избраните категории като JSON
            $('#selected-categories').val(JSON.stringify(module.selectedCategories));
            
            if (module.selectedCategories.length === 0) {
                container.html('<em>Няма избрани категории</em>');
                return;
            }
            
            $.each(module.selectedCategories, function(idx, category) {
                var categoryTag = $('<div class="category-tag"></div>')
                    .html('<span>' + category.name + '</span>')
                    .append('<button type="button" class="remove-category" data-id="' + category.id + '">&times;</button>');
                    
                container.append(categoryTag);
            });
            
            // Добавяме събитие за премахване на категория
            $('.remove-category').on('click', function() {
                var categoryId = parseInt($(this).data('id'), 10);
                module.removeCategory(categoryId);
            });
        },
        
        // Обновява кеша на фийда
        refreshFeedCache: function() {
            if (module.selectedCategories.length === 0) {
                alert('Моля, първо изберете и запазете категории преди да генерирате фийда!');
                return;
            }

            var $refreshButton = $('#refresh-feed');
            var $loader = $('#feed-refresh-loader');

            // Показваме лоудъра и деактивираме бутона
            $refreshButton.prop('disabled', true);
            $loader.show();

            var ajaxData = { vendor: module.currentVendor };

            base.ajaxCall({
                button: $refreshButton,
                disable_button: false // Не използваме вградената функционалност, защото управляваме бутона ръчно
            },
            'refreshFeedCache',
            ajaxData,
            function(success, response) {
                // Скриваме лоудъра и активираме бутона
                $loader.hide();
                $refreshButton.prop('disabled', false);

                if (success && response.data) {
                    var data = response.data;
                    if (data.status && data.status.success) {
                        alert('XML фийдът е генериран успешно! Размер: ' + data.status.size_formatted +
                              ', Брой продукти: ' + data.status.products_count);

                        // Актуализираме информацията за броя продукти
                        $('#products-count').text(data.status.products_count);

                        // Обновяваме статуса на кеша
                        module.loadCacheStatus();
                    } else {
                        alert('Фийдът е генериран, но има проблеми: ' +
                              (data.message ? data.message : 'неизвестна грешка'));
                    }
                } else {
                    alert('Възникна грешка при генериране на фийда: ' +
                          (response.data && response.data.message ? response.data.message : 'неизвестна грешка'));
                }
            });
        },
        
        // Инициализация на автоматично попълване на категории
        initializeCategoryAutocomplete: function() {
            module.searchInput = $('#category-search-input');
            if (!module.searchInput.length) {
                return; // Ако полето не съществува, не продължавай
            }

            module.suggestionsDiv = $('#category-suggestions');
            module.selectedCategoriesInput = $('#selected-categories');
            module.selectedCategoriesContainer = $('#selected-categories-container');

            module.bindCategoryAutocompleteEvents();
        },

        // Добавяме стилове за автоматично попълване и тагове
        addCategoryAutocompleteStyles: function() {
            var style = `
                .category-suggestion {
                    padding: 8px 12px;
                    cursor: pointer;
                    transition: background-color 0.2s;
                    border-bottom: 1px solid #eee;
                }
                .category-suggestion:last-child {
                    border-bottom: none;
                }
                .category-suggestion:hover {
                    background-color: #f0f7ff;
                    color: #0073aa;
                }
                .category-label {
                    font-weight: bold;
                }
                .category-tag {
                    display: inline-flex;
                    align-items: center;
                    background-color: #f0f7ff;
                    border: 1px solid #c6ddf4;
                    border-radius: 4px;
                    padding: 4px 8px;
                    margin-right: 8px;
                    margin-bottom: 8px;
                }
                .category-tag span {
                    margin-right: 8px;
                    font-size: 13px;
                    color: #2271b1;
                }
                .remove-category {
                    background: none;
                    border: none;
                    cursor: pointer;
                    color: #cc0000;
                    font-weight: bold;
                    font-size: 16px;
                    padding: 0 4px;
                    margin: 0;
                    line-height: 1;
                }
                .remove-category:hover {
                    color: #ff0000;
                }
                .status-ok {
                    color: #006600;
                    font-weight: bold;
                }
                .status-warning {
                    color: #ff6600;
                    font-weight: bold;
                }
                .status-error {
                    color: #cc0000;
                    font-weight: bold;
                }
                #feed-refresh-loader {
                    display: inline-flex;
                    align-items: center;
                }
                #feed-refresh-loader span {
                    color: #666;
                    font-style: italic;
                }
            `;
            $('<style>').html(style).appendTo('head');
        },

        // Добавяне на събития за автоматично попълване
        bindCategoryAutocompleteEvents: function() {
            module.searchInput.on('input', function() {
                clearTimeout(module.debounceTimer); // Изчистваме предишния таймер
                var searchTerm = $(this).val().trim();

                if (searchTerm.length < 2) {
                    module.suggestionsDiv.hide();
                    return;
                }

                module.debounceTimer = setTimeout(function() {
                    module.searchCategories(searchTerm);
                }, 300); // Добавяме забавяне от 300мс за да избегнем твърде много заявки
            });

            // Скриваме предложенията при клик извън тях
            $(document).on('click', function(e) {
                if (!module.searchInput.is(e.target) && !module.suggestionsDiv.is(e.target) && module.suggestionsDiv.has(e.target).length === 0) {
                    module.suggestionsDiv.hide();
                }
            });
        },

        // Търсене на категории с AJAX
        searchCategories: function(term) {
            var ajaxData = { term: term };
            base.ajaxCall({
                loader_container: module.suggestionsDiv,
                loader_html: '<div class="category-suggestion">Търсене...</div>',
                show_loader_container: true
            }, 
            'autoCompleteCategories', 
            ajaxData, 
            function(success, response) {
                module.suggestionsDiv.empty();

                if (success && response.data && response.data.length > 0) {
                    $.each(response.data, function(idx, category) {
                        var suggestionItem = $('<div class="category-suggestion" data-id="' + category.id + '">' +
                                            '<span class="category-label">' + category.name + '</span>' +
                                         '</div>');
                        
                        suggestionItem.on('click', function() {
                            // Добавяме категорията към избраните
                            if (module.addCategory(category.id, category.name)) {
                                module.searchInput.val(''); // Изчистваме полето след избиране
                            } else {
                                alert('Тази категория вече е добавена!');
                            }
                            module.suggestionsDiv.hide();
                        });
                        
                        module.suggestionsDiv.append(suggestionItem);
                    });
                    
                    module.suggestionsDiv.show();
                } else {
                    module.suggestionsDiv.html('<div class="category-suggestion">Няма намерени категории</div>');
                    module.suggestionsDiv.show();
                }
            });
        },
        
        // Обработка на AJAX отговори
        processResponse: function(success, response, resultContainer, resultContainerRow) {
            if(success && response && response.success) {
                resultContainerRow = resultContainerRow || 0;
                resultContainer = resultContainer || $('.result-container');
                resultContainer.eq(resultContainerRow).html('<div class="notice notice-success inline"><p>' + response.data.result + '</p></div>');
            }
            else if(!success && response && response.data && response.data.errors) {
                resultContainerRow = resultContainerRow || 0;
                resultContainer = resultContainer || $('.result-container');
                resultContainer.eq(resultContainerRow).html('<div class="notice notice-error inline">' + response.data.errors.join('<br>') + '</div>');
            } else {
                alert('Проблем с обработката на заявката!');
            }
        }
    };

    return module;
})(jQuery);

jQuery(document).ready(function() {
    AV_PS_Admin_Feeds.init();
});
