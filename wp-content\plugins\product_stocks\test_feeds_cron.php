<?php
/**
 * Тестов файл за проверка на функционалността за автоматично регенериране на XML фийдове
 * 
 * Този файл може да се използва за:
 * 1. Проверка на състоянието на cron задачата
 * 2. Тестово изпълнение на автоматичното обновяване
 * 3. Инициализиране на cron задачата
 * 4. Преглед на лог файловете
 * 
 * Използване: Отворете файла в браузъра с параметри:
 * - ?action=status - проверка на състоянието
 * - ?action=test - тестово изпълнение
 * - ?action=init - инициализиране на cron
 * - ?action=logs - преглед на логовете
 */

// Зареждаме WordPress
require_once('../../../wp-load.php');

// Проверяваме дали потребителят е администратор
if (!current_user_can('manage_options')) {
    wp_die('Нямате права за достъп до тази страница.');
}

$action = isset($_GET['action']) ? $_GET['action'] : 'status';

?>
<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест на Cron за XML Фийдове</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .nav { margin-bottom: 20px; }
        .nav a { display: inline-block; padding: 10px 15px; margin-right: 10px; background: #0073aa; color: white; text-decoration: none; border-radius: 3px; }
        .nav a:hover { background: #005a87; }
        .nav a.active { background: #00a0d2; }
        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .status.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .status.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .status.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .status.info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .log-content { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 500px; overflow-y: auto; }
        .button { display: inline-block; padding: 10px 20px; background: #0073aa; color: white; text-decoration: none; border-radius: 3px; border: none; cursor: pointer; }
        .button:hover { background: #005a87; }
        .button.danger { background: #dc3545; }
        .button.danger:hover { background: #c82333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Тест на Cron за XML Фийдове</h1>
        
        <div class="nav">
            <a href="?action=status" class="<?php echo $action === 'status' ? 'active' : ''; ?>">📊 Състояние</a>
            <a href="?action=test" class="<?php echo $action === 'test' ? 'active' : ''; ?>">🧪 Тестово изпълнение</a>
            <a href="?action=init" class="<?php echo $action === 'init' ? 'active' : ''; ?>">🚀 Инициализиране</a>
            <a href="?action=logs" class="<?php echo $action === 'logs' ? 'active' : ''; ?>">📄 Логове</a>
        </div>

        <?php
        switch ($action) {
            case 'status':
                show_cron_status();
                break;
            case 'test':
                test_feeds_refresh();
                break;
            case 'init':
                init_feeds_cron();
                break;
            case 'logs':
                show_logs();
                break;
            default:
                show_cron_status();
        }
        ?>
    </div>
</body>
</html>

<?php

function show_cron_status() {
    echo '<h2>📊 Състояние на Cron задачите</h2>';
    
    // Проверяваме cron за XML фийдове
    $feeds_cron = wp_next_scheduled('av_ps_feeds_auto_refresh');
    
    if ($feeds_cron) {
        echo '<div class="status success">';
        echo '<strong>✅ Cron за XML фийдове (av_ps_feeds_auto_refresh)</strong><br>';
        echo 'Статус: <strong>Активен</strong><br>';
        echo 'Следващо изпълнение: <strong>' . date('Y-m-d H:i:s', $feeds_cron) . '</strong><br>';
        
        $event = wp_get_scheduled_event('av_ps_feeds_auto_refresh');
        if ($event) {
            echo 'Интервал: <strong>' . ($event->schedule ?? 'еднократно') . '</strong><br>';
            if (isset($event->interval)) {
                echo 'Интервал в секунди: <strong>' . $event->interval . '</strong> (' . ($event->interval / 3600) . ' часа)<br>';
            }
        }
        echo '</div>';
    } else {
        echo '<div class="status error">';
        echo '<strong>❌ Cron за XML фийдове (av_ps_feeds_auto_refresh)</strong><br>';
        echo 'Статус: <strong>Не е активен</strong><br>';
        echo '</div>';
    }
    
    // Проверяваме основния cron
    $hourly_cron = wp_next_scheduled('av_ps_hourly_cron_hook');
    
    if ($hourly_cron) {
        echo '<div class="status info">';
        echo '<strong>ℹ️ Основен cron (av_ps_hourly_cron_hook)</strong><br>';
        echo 'Статус: <strong>Активен</strong><br>';
        echo 'Следващо изпълнение: <strong>' . date('Y-m-d H:i:s', $hourly_cron) . '</strong><br>';
        echo '</div>';
    } else {
        echo '<div class="status warning">';
        echo '<strong>⚠️ Основен cron (av_ps_hourly_cron_hook)</strong><br>';
        echo 'Статус: <strong>Не е активен</strong><br>';
        echo '</div>';
    }
    
    // Проверяваме активните търговци
    if (class_exists('AV_PS_Rest_Feed_Controller')) {
        $controller = new AV_PS_Rest_Feed_Controller();
        $reflection = new ReflectionClass($controller);
        $method = $reflection->getMethod('getActiveVendors');
        $method->setAccessible(true);
        $active_vendors = $method->invoke($controller);
        
        if (!empty($active_vendors)) {
            echo '<div class="status success">';
            echo '<strong>👥 Активни търговци</strong><br>';
            echo 'Брой: <strong>' . count($active_vendors) . '</strong><br>';
            echo 'Търговци: <strong>' . implode(', ', $active_vendors) . '</strong><br>';
            echo '</div>';
        } else {
            echo '<div class="status warning">';
            echo '<strong>⚠️ Активни търговци</strong><br>';
            echo 'Няма настроени търговци с категории<br>';
            echo '</div>';
        }
    }
}

function test_feeds_refresh() {
    echo '<h2>🧪 Тестово изпълнение на автоматичното обновяване</h2>';
    
    if (!class_exists('AV_PS_Rest_Feed_Controller')) {
        echo '<div class="status error">❌ Класът AV_PS_Rest_Feed_Controller не е намерен</div>';
        return;
    }
    
    echo '<div class="status info">ℹ️ Стартиране на тестово автоматично обновяване...</div>';
    
    try {
        $start_time = microtime(true);
        
        $rest_controller = new AV_PS_Rest_Feed_Controller();
        $rest_controller->auto_refresh_feeds();
        
        $execution_time = round(microtime(true) - $start_time, 2);
        
        echo '<div class="status success">';
        echo '✅ Тестовото обновяване е завършено успешно!<br>';
        echo 'Време за изпълнение: <strong>' . $execution_time . ' секунди</strong><br>';
        echo 'Проверете лог файла за детайли.<br>';
        echo '</div>';
        
        echo '<p><a href="?action=logs" class="button">📄 Виж логовете</a></p>';
        
    } catch (Exception $e) {
        echo '<div class="status error">❌ Грешка при тестовото изпълнение: ' . $e->getMessage() . '</div>';
    }
}

function init_feeds_cron() {
    echo '<h2>🚀 Инициализиране на Cron задача</h2>';
    
    try {
        // Проверяваме дали вече има планирана задача
        $existing_cron = wp_next_scheduled('av_ps_feeds_auto_refresh');
        if ($existing_cron) {
            echo '<div class="status warning">';
            echo '⚠️ Cron задачата вече съществува!<br>';
            echo 'Следващо изпълнение: <strong>' . date('Y-m-d H:i:s', $existing_cron) . '</strong><br>';
            echo '</div>';
            
            echo '<p><a href="?action=reinit" class="button danger" onclick="return confirm(\'Сигурни ли сте, че искате да преинициализирате cron задачата?\')">🔄 Преинициализирай</a></p>';
        } else {
            echo '<div class="status info">ℹ️ Cron задачата не съществува. Създаване...</div>';
            
            // Създаваме инстанция на основния клас
            $plugin = new AV_ProductStocks();
            
            // Инициализираме cron задачата
            $plugin->init_feeds_auto_refresh_cron();
            
            // Проверяваме дали е създадена успешно
            $new_cron = wp_next_scheduled('av_ps_feeds_auto_refresh');
            if ($new_cron) {
                echo '<div class="status success">';
                echo '✅ Cron задачата е създадена успешно!<br>';
                echo 'Следващо изпълнение: <strong>' . date('Y-m-d H:i:s', $new_cron) . '</strong><br>';
                echo '</div>';
            } else {
                echo '<div class="status error">❌ Грешка при създаване на cron задачата</div>';
            }
        }
        
        // Ако е заявено преинициализиране
        if (isset($_GET['action']) && $_GET['action'] === 'reinit') {
            wp_clear_scheduled_hook('av_ps_feeds_auto_refresh');
            echo '<div class="status info">ℹ️ Старата cron задача е премахната. Създаване на нова...</div>';
            
            $plugin = new AV_ProductStocks();
            $plugin->init_feeds_auto_refresh_cron();
            
            $new_cron = wp_next_scheduled('av_ps_feeds_auto_refresh');
            if ($new_cron) {
                echo '<div class="status success">';
                echo '✅ Новата cron задача е създадена успешно!<br>';
                echo 'Следващо изпълнение: <strong>' . date('Y-m-d H:i:s', $new_cron) . '</strong><br>';
                echo '</div>';
            } else {
                echo '<div class="status error">❌ Грешка при създаване на новата cron задача</div>';
            }
        }
        
    } catch (Exception $e) {
        echo '<div class="status error">❌ Грешка: ' . $e->getMessage() . '</div>';
    }
}

function show_logs() {
    echo '<h2>📄 Логове за автоматично обновяване</h2>';
    
    $log_file = AV_PS()->Data->getData()['data_dir'] . 'logs/feeds_auto_refresh.txt';
    
    if (file_exists($log_file)) {
        $log_content = file_get_contents($log_file);
        
        if (!empty($log_content)) {
            // Показваме последните 100 реда
            $lines = explode("\n", $log_content);
            $recent_lines = array_slice($lines, -100);
            $recent_content = implode("\n", $recent_lines);
            
            echo '<div class="status info">';
            echo 'Файл: <strong>' . $log_file . '</strong><br>';
            echo 'Размер: <strong>' . size_format(filesize($log_file)) . '</strong><br>';
            echo 'Последна промяна: <strong>' . date('Y-m-d H:i:s', filemtime($log_file)) . '</strong><br>';
            echo 'Показани са последните 100 реда<br>';
            echo '</div>';
            
            echo '<div class="log-content">' . esc_html($recent_content) . '</div>';
            
            echo '<p>';
            echo '<a href="?action=logs&download=1" class="button">💾 Изтегли пълния лог</a> ';
            echo '<a href="?action=logs&clear=1" class="button danger" onclick="return confirm(\'Сигурни ли сте, че искате да изчистите лог файла?\')">🗑️ Изчисти лога</a>';
            echo '</p>';
        } else {
            echo '<div class="status warning">⚠️ Лог файлът е празен</div>';
        }
    } else {
        echo '<div class="status error">❌ Лог файлът не съществува: ' . $log_file . '</div>';
    }
    
    // Обработка на действия
    if (isset($_GET['download']) && $_GET['download'] === '1') {
        if (file_exists($log_file)) {
            header('Content-Type: text/plain');
            header('Content-Disposition: attachment; filename="feeds_auto_refresh_' . date('Y-m-d_H-i-s') . '.txt"');
            readfile($log_file);
            exit;
        }
    }
    
    if (isset($_GET['clear']) && $_GET['clear'] === '1') {
        if (file_exists($log_file)) {
            file_put_contents($log_file, '');
            echo '<div class="status success">✅ Лог файлът е изчистен успешно</div>';
            echo '<script>setTimeout(function(){ window.location.href = "?action=logs"; }, 2000);</script>';
        }
    }
}
?>
